<template>
  <div class="wh-full">
    <AmisEditor
      id="editorName"
      theme="cxd"
      className="test-amis-editor"
      :preview="previewModel"
      :is-mobild="mobileModel"
      :value="schema"
      :onChange="editorChanged"
    />
  </div>
</template>

<script lang="ts" setup>
import 'amis-ui/lib/themes/default.css'
import 'amis-ui/lib/themes/cxd.css'
import 'amis-editor-core/lib/style.css'

import { applyReactInVue } from 'veaury'
import { Editor } from 'amis-editor'

const AmisEditor = applyReactInVue(Editor)
const previewModel = ref(false)
const mobileModel = ref(false)

const schema = reactive({}) //渲染表单的内容

const editorChanged = (value) => {
  //编辑器内容变化后触发的方法
  console.log('编辑器内容变化了。。。。', value)
  //todo  如果需要将数据保存，在这里可以操作
}
</script>
